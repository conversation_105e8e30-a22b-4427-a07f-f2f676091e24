# -*- coding: utf-8 -*-
import pandas as pd
from collections import Counter

def remove_duplicate_student_ids():
    """去除学号相同的数据"""
    # 读取试听管理Excel文件
    file_path = '/Users/<USER>/Documents/Myproject/tg-dem/试听管理_2025年07月16日18时31分.xlsx'
    df = pd.read_excel(file_path)
    
    print("原始数据统计:")
    print("总记录数: {}".format(len(df)))
    print("列名: {}".format(list(df.columns)))
    
    # 检查学号列
    student_id_column = '学号'
    if student_id_column not in df.columns:
        print("未找到学号列")
        return None
    
    # 统计重复学号
    duplicate_count = df[student_id_column].duplicated().sum()
    unique_count = df[student_id_column].nunique()
    
    print("\n重复数据分析:")
    print("唯一学号数: {}".format(unique_count))
    print("重复记录数: {}".format(duplicate_count))
    
    # 显示重复学号的详细信息
    duplicated_ids = df[df[student_id_column].duplicated(keep=False)]
    if len(duplicated_ids) > 0:
        print("\n重复学号详情 (前10个):")
        duplicate_summary = duplicated_ids.groupby(student_id_column).size().sort_values(ascending=False)
        for student_id, count in duplicate_summary.head(10).items():
            print("学号 {}: {} 条记录".format(student_id, count))
            
        # 显示重复学号的具体记录示例
        print("\n重复记录示例:")
        example_id = duplicate_summary.index[0]
        example_records = df[df[student_id_column] == example_id][['学员姓名', '学号', '所属校区', '意向录入日期']]
        print("学号 {} 的所有记录:".format(example_id))
        for idx, row in example_records.iterrows():
            print("  - 姓名: {}, 校区: {}, 录入日期: {}".format(
                row['学员姓名'], row['所属校区'], row['意向录入日期']))
    
    # 去除重复数据，保留第一条记录
    df_cleaned = df.drop_duplicates(subset=[student_id_column], keep='first')
    
    print("\n去重后统计:")
    print("剩余记录数: {}".format(len(df_cleaned)))
    print("删除记录数: {}".format(len(df) - len(df_cleaned)))
    
    # 保存清理后的数据
    output_file = '/Users/<USER>/Documents/Myproject/tg-dem/试听管理_去重后.xlsx'
    df_cleaned.to_excel(output_file, index=False)
    print("\n已保存去重后的数据到: {}".format(output_file))

    # 提取并保存重复数据
    if len(duplicated_ids) > 0:
        duplicate_file = '/Users/<USER>/Documents/Myproject/tg-dem/重复数据记录.xlsx'
        duplicated_ids.to_excel(duplicate_file, index=False)
        print("已保存重复数据到: {}".format(duplicate_file))
        print("重复数据包含 {} 条记录".format(len(duplicated_ids)))

    return df_cleaned

def analyze_duplicate_patterns(df):
    """分析重复数据的模式"""
    student_id_column = '学号'
    
    # 找出所有重复的学号
    duplicated_ids = df[df[student_id_column].duplicated(keep=False)]
    
    if len(duplicated_ids) == 0:
        print("没有发现重复的学号")
        return
    
    print("\n重复数据模式分析:")
    
    # 按学号分组分析
    duplicate_groups = duplicated_ids.groupby(student_id_column)
    
    # 分析重复原因
    same_name_count = 0
    different_name_count = 0
    same_campus_count = 0
    different_campus_count = 0
    
    for student_id, group in duplicate_groups:
        names = group['学员姓名'].unique()
        campuses = group['所属校区'].unique()
        
        if len(names) == 1:
            same_name_count += 1
        else:
            different_name_count += 1
            
        if len(campuses) == 1:
            same_campus_count += 1
        else:
            different_campus_count += 1
    
    print("重复学号中:")
    print("- 同姓名的: {} 个学号".format(same_name_count))
    print("- 不同姓名的: {} 个学号".format(different_name_count))
    print("- 同校区的: {} 个学号".format(same_campus_count))
    print("- 不同校区的: {} 个学号".format(different_campus_count))
    
    # 显示不同姓名但相同学号的情况
    if different_name_count > 0:
        print("\n可能的数据错误 - 相同学号但不同姓名:")
        for student_id, group in duplicate_groups:
            names = group['学员姓名'].unique()
            if len(names) > 1:
                print("学号 {}: 姓名 {}".format(student_id, ', '.join(names)))

if __name__ == "__main__":
    # 读取原始数据
    file_path = '/Users/<USER>/Documents/Myproject/tg-dem/试听管理_2025年07月16日18时31分.xlsx'
    df = pd.read_excel(file_path)
    
    # 分析重复模式
    analyze_duplicate_patterns(df)
    
    # 去除重复数据
    df_cleaned = remove_duplicate_student_ids()
    
    if df_cleaned is not None:
        print("\n数据清理完成！")
