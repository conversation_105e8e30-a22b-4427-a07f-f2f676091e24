# -*- coding: utf-8 -*-
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def remove_duplicate_student_ids():
    """去除学号相同的数据"""
    # 读取试听管理Excel文件
    file_path = '/Users/<USER>/Documents/Myproject/tg-dem/试听管理_2025年07月16日18时31分.xlsx'
    df = pd.read_excel(file_path)

    print("原始数据统计:")
    print("总记录数: {}".format(len(df)))

    # 检查学号列
    student_id_column = '学号'
    if student_id_column not in df.columns:
        print("未找到学号列")
        return

    # 统计重复学号
    duplicate_count = df[student_id_column].duplicated().sum()
    unique_count = df[student_id_column].nunique()

    print("唯一学号数: {}".format(unique_count))
    print("重复记录数: {}".format(duplicate_count))

    # 显示重复学号的详细信息
    duplicated_ids = df[df[student_id_column].duplicated(keep=False)]
    if len(duplicated_ids) > 0:
        print("\n重复学号详情:")
        duplicate_summary = duplicated_ids.groupby(student_id_column).size().sort_values(ascending=False)
        for student_id, count in duplicate_summary.head(10).items():
            print("学号 {}: {} 条记录".format(student_id, count))

    # 去除重复数据，保留第一条记录
    df_cleaned = df.drop_duplicates(subset=[student_id_column], keep='first')

    print("\n去重后统计:")
    print("剩余记录数: {}".format(len(df_cleaned)))
    print("删除记录数: {}".format(len(df) - len(df_cleaned)))

    return df_cleaned

def analyze_student_data():
    # 读取Excel文件
    file_path = '/Users/<USER>/Documents/Myproject/tg-dem/files/学员信息管理_2025年07月09日11时28分.xlsx'
    df = pd.read_excel(file_path)

    print("总学员数: {}".format(len(df)))

    # 分析聂道棋力
    nie_dao_column = '聂道棋力'
    if nie_dao_column in df.columns:
        nie_dao_data = df[nie_dao_column].dropna()
        nie_dao_data = nie_dao_data[nie_dao_data != '']  # 去除空字符串

        print("有聂道棋力记录的学员数: {}".format(len(nie_dao_data)))

        # 统计各等级分布
        nie_dao_counts = Counter(nie_dao_data)

        print("\n聂道等级分布:")
        for level, count in sorted(nie_dao_counts.items()):
            percentage = (count / len(nie_dao_data)) * 100
            print("{}: {}人 ({:.1f}%)".format(level, count, percentage))
    else:
        print("未找到聂道棋力列")
        nie_dao_counts = {}
        nie_dao_data = []

    # 分析证书棋力
    cert_column = '证书棋力'
    if cert_column in df.columns:
        cert_data = df[cert_column].dropna()
        cert_data = cert_data[cert_data != '']  # 去除空字符串

        print("\n有证书棋力记录的学员数: {}".format(len(cert_data)))

        # 统计各等级分布
        cert_counts = Counter(cert_data)

        print("\n证书棋力等级分布:")
        for level, count in sorted(cert_counts.items()):
            percentage = (count / len(cert_data)) * 100
            print("{}: {}人 ({:.1f}%)".format(level, count, percentage))
    else:
        print("未找到证书棋力列")
        cert_counts = {}
        cert_data = []

    return nie_dao_counts, nie_dao_data, cert_counts, cert_data

def create_charts(nie_dao_counts, nie_dao_data, cert_counts, cert_data):
    # 创建图表 - 聂道棋力
    if nie_dao_counts:
        fig1, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig1.suptitle('学员聂道棋力分布分析', fontsize=16, fontweight='bold')
    
        # 1. 柱状图
        levels = list(nie_dao_counts.keys())
        counts = list(nie_dao_counts.values())

        bars = ax1.bar(levels, counts, color='skyblue', edgecolor='navy', alpha=0.7)
        ax1.set_title('聂道等级分布 - 柱状图', fontsize=12, fontweight='bold')
        ax1.set_xlabel('聂道等级', fontsize=10)
        ax1.set_ylabel('学员人数', fontsize=10)
        ax1.grid(axis='y', alpha=0.3)

        # 在柱子上添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    '{}'.format(int(height)), ha='center', va='bottom', fontsize=9)

        # 2. 饼图
        colors = plt.cm.Set3(np.linspace(0, 1, len(levels)))
        wedges, texts, autotexts = ax2.pie(counts, labels=levels, autopct='%1.1f%%',
                                           colors=colors, startangle=90)
        ax2.set_title('聂道等级分布 - 饼图', fontsize=12, fontweight='bold')

        # 3. 水平柱状图
        y_pos = np.arange(len(levels))
        bars_h = ax3.barh(y_pos, counts, color='lightcoral', edgecolor='darkred', alpha=0.7)
        ax3.set_yticks(y_pos)
        ax3.set_yticklabels(levels)
        ax3.set_xlabel('学员人数', fontsize=10)
        ax3.set_title('聂道等级分布 - 水平柱状图', fontsize=12, fontweight='bold')
        ax3.grid(axis='x', alpha=0.3)

        # 在水平柱子上添加数值标签
        for bar in bars_h:
            width = bar.get_width()
            ax3.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                    '{}'.format(int(width)), ha='left', va='center', fontsize=9)

        # 4. 环形图
        wedges, texts = ax4.pie(counts, labels=levels, colors=colors, startangle=90,
                               wedgeprops=dict(width=0.5))

        # 在中心添加总数信息
        ax4.text(0, 0, '总计\n{}人'.format(sum(counts)), ha='center', va='center',
                 fontsize=10, fontweight='bold')
        ax4.set_title('聂道等级分布 - 环形图', fontsize=12, fontweight='bold')

        plt.tight_layout()
        plt.savefig('/Users/<USER>/Documents/Myproject/tg-dem/student_nie_dao_distribution.png',
                    dpi=300, bbox_inches='tight')
        plt.show()

    # 创建证书棋力图表
    if cert_counts:
        fig2, ((ax5, ax6), (ax7, ax8)) = plt.subplots(2, 2, figsize=(15, 12))
        fig2.suptitle('学员证书棋力分布分析', fontsize=16, fontweight='bold')

        cert_levels = list(cert_counts.keys())
        cert_count_values = list(cert_counts.values())

        # 1. 柱状图
        bars2 = ax5.bar(cert_levels, cert_count_values, color='lightgreen', edgecolor='darkgreen', alpha=0.7)
        ax5.set_title('证书棋力分布 - 柱状图', fontsize=12, fontweight='bold')
        ax5.set_xlabel('证书等级', fontsize=10)
        ax5.set_ylabel('学员人数', fontsize=10)
        ax5.grid(axis='y', alpha=0.3)

        # 在柱子上添加数值标签
        for bar in bars2:
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    '{}'.format(int(height)), ha='center', va='bottom', fontsize=9)

        # 2. 饼图
        colors2 = plt.cm.Pastel1(np.linspace(0, 1, len(cert_levels)))
        wedges2, texts2, autotexts2 = ax6.pie(cert_count_values, labels=cert_levels, autopct='%1.1f%%',
                                              colors=colors2, startangle=90)
        ax6.set_title('证书棋力分布 - 饼图', fontsize=12, fontweight='bold')

        # 3. 水平柱状图
        y_pos2 = np.arange(len(cert_levels))
        bars_h2 = ax7.barh(y_pos2, cert_count_values, color='orange', edgecolor='darkorange', alpha=0.7)
        ax7.set_yticks(y_pos2)
        ax7.set_yticklabels(cert_levels)
        ax7.set_xlabel('学员人数', fontsize=10)
        ax7.set_title('证书棋力分布 - 水平柱状图', fontsize=12, fontweight='bold')
        ax7.grid(axis='x', alpha=0.3)

        # 在水平柱子上添加数值标签
        for bar in bars_h2:
            width = bar.get_width()
            ax7.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                    '{}'.format(int(width)), ha='left', va='center', fontsize=9)

        # 4. 环形图
        wedges2, texts2 = ax8.pie(cert_count_values, labels=cert_levels, colors=colors2, startangle=90,
                                 wedgeprops=dict(width=0.5))

        # 在中心添加总数信息
        ax8.text(0, 0, '总计\n{}人'.format(sum(cert_count_values)), ha='center', va='center',
                 fontsize=10, fontweight='bold')
        ax8.set_title('证书棋力分布 - 环形图', fontsize=12, fontweight='bold')

        plt.tight_layout()
        plt.savefig('/Users/<USER>/Documents/Myproject/tg-dem/student_cert_distribution.png',
                    dpi=300, bbox_inches='tight')
        plt.show()

if __name__ == "__main__":
    nie_dao_counts, nie_dao_data, cert_counts, cert_data = analyze_student_data()
    create_charts(nie_dao_counts, nie_dao_data, cert_counts, cert_data)
