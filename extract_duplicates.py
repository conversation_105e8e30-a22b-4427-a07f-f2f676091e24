# -*- coding: utf-8 -*-
import pandas as pd
from collections import Counter

def extract_duplicate_data():
    """提取重复数据并生成详细的Excel报告"""
    # 读取试听管理Excel文件
    file_path = '/Users/<USER>/Documents/Myproject/tg-dem/试听管理_2025年07月16日18时31分.xlsx'
    df = pd.read_excel(file_path)
    
    print("开始提取重复数据...")
    print("原始数据总记录数: {}".format(len(df)))
    
    # 检查学号列
    student_id_column = '学号'
    if student_id_column not in df.columns:
        print("未找到学号列")
        return None
    
    # 找出所有重复的学号记录
    duplicated_mask = df[student_id_column].duplicated(keep=False)
    duplicated_records = df[duplicated_mask].copy()
    
    if len(duplicated_records) == 0:
        print("没有发现重复数据")
        return None
    
    print("发现重复记录数: {}".format(len(duplicated_records)))
    
    # 按学号排序，便于查看
    duplicated_records = duplicated_records.sort_values([student_id_column, '意向录入日期'])
    
    # 添加重复序号列
    duplicated_records['重复序号'] = duplicated_records.groupby(student_id_column).cumcount() + 1
    
    # 添加该学号总重复次数列
    duplicate_counts = duplicated_records.groupby(student_id_column).size()
    duplicated_records['该学号重复总数'] = duplicated_records[student_id_column].map(duplicate_counts)
    
    # 重新排列列的顺序，将重复信息放在前面
    cols = list(duplicated_records.columns)
    # 移动重复相关列到前面
    cols.remove('重复序号')
    cols.remove('该学号重复总数')
    new_cols = ['学号', '重复序号', '该学号重复总数'] + [col for col in cols if col != '学号']
    duplicated_records = duplicated_records[new_cols]
    
    return duplicated_records

def create_duplicate_analysis():
    """创建重复数据分析摘要"""
    file_path = '/Users/<USER>/Documents/Myproject/tg-dem/试听管理_2025年07月16日18时31分.xlsx'
    df = pd.read_excel(file_path)
    
    student_id_column = '学号'
    duplicated_mask = df[student_id_column].duplicated(keep=False)
    duplicated_records = df[duplicated_mask]
    
    if len(duplicated_records) == 0:
        return None
    
    # 创建分析摘要
    analysis_data = []
    
    # 按学号分组分析
    for student_id, group in duplicated_records.groupby(student_id_column):
        record_count = len(group)
        names = group['学员姓名'].unique()
        campuses = group['所属校区'].unique()
        dates = group['意向录入日期'].unique()
        phones = group['手机号'].unique()
        
        analysis_data.append({
            '学号': student_id,
            '重复次数': record_count,
            '学员姓名': ', '.join(str(name) for name in names),
            '姓名是否一致': '是' if len(names) == 1 else '否',
            '所属校区': ', '.join(str(campus) for campus in campuses),
            '校区是否一致': '是' if len(campuses) == 1 else '否',
            '录入日期': ', '.join(str(date) for date in dates),
            '日期是否一致': '是' if len(dates) == 1 else '否',
            '手机号': ', '.join(str(phone) for phone in phones),
            '手机号是否一致': '是' if len(phones) == 1 else '否',
            '重复类型': '完全重复' if len(names) == 1 and len(campuses) == 1 and len(dates) == 1 else '部分重复'
        })
    
    analysis_df = pd.DataFrame(analysis_data)
    return analysis_df

def save_duplicate_excel():
    """保存重复数据到Excel文件，包含多个工作表"""
    
    # 提取重复数据
    duplicate_records = extract_duplicate_data()
    if duplicate_records is None:
        print("没有重复数据需要保存")
        return
    
    # 创建分析摘要
    analysis_summary = create_duplicate_analysis()
    
    # 保存到Excel文件，包含多个工作表
    output_file = '/Users/<USER>/Documents/Myproject/tg-dem/重复数据详细报告.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 工作表1：重复数据详细记录
        duplicate_records.to_excel(writer, sheet_name='重复数据详细记录', index=False)
        
        # 工作表2：重复数据分析摘要
        if analysis_summary is not None:
            analysis_summary.to_excel(writer, sheet_name='重复数据分析摘要', index=False)
        
        # 工作表3：统计信息
        stats_data = {
            '统计项目': [
                '原始数据总记录数',
                '重复记录总数',
                '涉及的重复学号数量',
                '平均每个学号重复次数',
                '最大重复次数',
                '完全重复记录数',
                '部分重复记录数'
            ],
            '数值': [
                len(pd.read_excel('/Users/<USER>/Documents/Myproject/tg-dem/试听管理_2025年07月16日18时31分.xlsx')),
                len(duplicate_records),
                duplicate_records['学号'].nunique(),
                round(len(duplicate_records) / duplicate_records['学号'].nunique(), 2),
                duplicate_records['该学号重复总数'].max(),
                len(analysis_summary[analysis_summary['重复类型'] == '完全重复']) if analysis_summary is not None else 0,
                len(analysis_summary[analysis_summary['重复类型'] == '部分重复']) if analysis_summary is not None else 0
            ]
        }
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)
    
    print("\n重复数据Excel报告已生成: {}".format(output_file))
    print("包含以下工作表:")
    print("1. 重复数据详细记录 - 所有重复记录的完整信息")
    print("2. 重复数据分析摘要 - 按学号汇总的重复情况分析")
    print("3. 统计信息 - 重复数据的统计概览")
    
    # 显示重复数据概览
    print("\n重复数据概览:")
    if analysis_summary is not None:
        for _, row in analysis_summary.iterrows():
            print("学号 {}: 重复{}次, 类型: {}".format(
                row['学号'], row['重复次数'], row['重复类型']))

if __name__ == "__main__":
    save_duplicate_excel()
